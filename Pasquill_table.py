# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/18 0:53
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON>ck’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""


def determine_pasquill_level(wind_speed=3, sun_rad_index=1):
    if wind_speed <= 2:
        if sun_rad_index == 3:
            pasquill_level = 'A'
        elif sun_rad_index == 2:
            pasquill_level = 'A'
        elif sun_rad_index == 1:
            pasquill_level = 'B'
        elif sun_rad_index == 0:
            pasquill_level = 'D'
        elif sun_rad_index == -1:
            pasquill_level = 'E'
        elif sun_rad_index == -2:
            pasquill_level = 'F'
        else:
            pasquill_level = None
    elif 2 < wind_speed <= 3:
        if sun_rad_index == 3:
            pasquill_level = 'B'
        elif sun_rad_index == 2:
            pasquill_level = 'B'
        elif sun_rad_index == 1:
            pasquill_level = 'C'
        elif sun_rad_index == 0:
            pasquill_level = 'D'
        elif sun_rad_index == -1:
            pasquill_level = 'E'
        elif sun_rad_index == -2:
            pasquill_level = 'F'
        else:
            pasquill_level = None
    elif 3 < wind_speed <= 5:
        if sun_rad_index == 3:
            pasquill_level = 'B'
        elif sun_rad_index == 2:
            pasquill_level = 'C'
        elif sun_rad_index == 1:
            pasquill_level = 'C'
        elif sun_rad_index == 0:
            pasquill_level = 'D'
        elif sun_rad_index == -1:
            pasquill_level = 'D'
        elif sun_rad_index == -2:
            pasquill_level = 'E'
        else:
            pasquill_level = None
    elif 5 < wind_speed <= 6:
        if sun_rad_index == 3:
            pasquill_level = 'C'
        elif sun_rad_index == 2:
            pasquill_level = 'D'
        elif sun_rad_index == 1:
            pasquill_level = 'C'
        elif sun_rad_index == 0:
            pasquill_level = 'D'
        elif sun_rad_index == -1:
            pasquill_level = 'D'
        elif sun_rad_index == -2:
            pasquill_level = 'D'
        else:
            pasquill_level = None
    else:   # wind_speed > 6
        if sun_rad_index == 3:
            pasquill_level = 'D'
        elif sun_rad_index == 2:
            pasquill_level = 'D'
        elif sun_rad_index == 1:
            pasquill_level = 'D'
        elif sun_rad_index == 0:
            pasquill_level = 'D'
        elif sun_rad_index == -1:
            pasquill_level = 'D'
        elif sun_rad_index == -2:
            pasquill_level = 'D'
        else:
            pasquill_level = None

    return pasquill_level
