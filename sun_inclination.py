# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/18 0:15
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON><PERSON>’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""

import datetime
import numpy as np


def determine_date_ordinal():
    year = datetime.datetime.today().year
    month = datetime.datetime.today().month
    day = datetime.datetime.today().day

    date = datetime.date(year, month, day)
    date_ordinal = int(date.strftime('%j'))     # %j十进制表示的每年的第几天

    return date_ordinal


def cal_sun_inclination(latitude=25, longitude=118):
    dn = determine_date_ordinal()
    t = datetime.datetime.today().hour
    theta = 360 * dn / 365
    delta = (0.006918 - 0.399912 * np.cos(theta) + 0.070257 * np.sin(theta) - 0.006758 * np.cos(2 * theta) +
             0.000907 * np.sin(2 * theta) - 0.002697 * np.cos(3 * theta) + 0.00148 * np.sin(3 * theta))
    h0 = np.arcsin(np.sin(latitude) * np.sin(delta) +
                   np.cos(latitude) * np.cos(delta) * np.cos(15 * t + longitude - 300))

    return h0
