# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/17 23:48
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON>ck’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""


def cal_sun_rad_index(tc=5, lc=4, if_day=True, solar_altitude=30):
    """
    :param tc: total cloudage
    :param lc: lower cloudage
    :param if_day: day or night
    :param solar_altitude: angle to the sun
    :return:
    """
    tc = int(tc)
    lc = int(lc)
    if not if_day:
        if tc <= 4 and lc <= 4:
            ci = -2
        elif 5 <= tc <= 7 and lc <= 4:
            ci = -1
        elif tc >= 8 and lc <= 4:
            ci = -1
        elif tc >= 5 and 5 <= lc <= 7:
            ci = 0
        elif tc >= 8 and lc >= 8:
            ci = 0
        else:
            ci = None
    else:
        if solar_altitude <= 15:
            if tc <= 4 and lc <= 4:
                ci = -1
            elif 5 <= tc <= 7 and lc <= 4:
                ci = 0
            elif tc >= 8 and lc <= 4:
                ci = 0
            elif tc >= 5 and 5 <= lc <= 7:
                ci = 0
            elif tc >= 8 and lc >= 8:
                ci = 0
            else:
                ci = None
        elif 15 < solar_altitude <= 35:
            if tc <= 4 and lc <= 4:
                ci = 1
            elif 5 <= tc <= 7 and lc <= 4:
                ci = 1
            elif tc >= 8 and lc <= 4:
                ci = 0
            elif tc >= 5 and 5 <= lc <= 7:
                ci = 0
            elif tc >= 8 and lc >= 8:
                ci = 0
            else:
                ci = None
        elif 35 < solar_altitude <= 65:
            if tc <= 4 and lc <= 4:
                ci = 2
            elif 5 <= tc <= 7 and lc <= 4:
                ci = 2
            elif tc >= 8 and lc <= 4:
                ci = 1
            elif tc >= 5 and 5 <= lc <= 7:
                ci = 0
            elif tc >= 8 and lc >= 8:
                ci = 0
            else:
                ci = None
        else:   # solar_altitude > 65
            if tc <= 4 and lc <= 4:
                ci = 3
            elif 5 <= tc <= 7 and lc <= 4:
                ci = 3
            elif tc >= 8 and lc <= 4:
                ci = 1
            elif tc >= 5 and 5 <= lc <= 7:
                ci = 1
            elif tc >= 8 and lc >= 8:
                ci = 0
            else:
                ci = None

    return ci
