# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/18 1:59
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON>ck’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""

import numpy


def model(X, Y, Z, sigma_y, sigma_z, t=300, leaking_loc=(0, 0),
          h=5, q=0.1, wind_speed=1, diffusion_speed=0.1, alpha=1):
    Y -= leaking_loc[1]
    psi = (q * (t - X / (wind_speed - diffusion_speed)) / (2 * numpy.pi * sigma_y * sigma_z)) * \
          numpy.exp(-0.5 * Y ** 2 / sigma_y ** 2) * (numpy.exp(-0.5 * (Z - h) ** 2 / sigma_z ** 2) +
                                                     alpha * numpy.exp(-0.5 * (Z + h) ** 2 / sigma_z ** 2))

    return psi
