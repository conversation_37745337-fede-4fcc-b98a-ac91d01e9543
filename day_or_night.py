# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/18 0:11
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON><PERSON>’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""

from datetime import datetime


def determine_day_or_night():
    hour = datetime.today().hour
    if 6 <= hour <= 18:
        if_day = True
    else:
        if_day = False

    return if_day
