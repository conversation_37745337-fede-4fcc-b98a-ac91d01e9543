# -*- coding: utf-8 -*-

"""
@Project: Diffusion
@Time: 2022/5/18 1:19
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON>ck’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""


def determine_briggs_diffusion_coefficient(x_array, pasquill_level='A', if_urban=True):
    if not if_urban:
        if pasquill_level == 'A':
            sigma_y_array = 0.22 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.2 * x_array
        elif pasquill_level == 'B':
            sigma_y_array = 0.16 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.12 * x_array
        elif pasquill_level == 'C':
            sigma_y_array = 0.11 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.08 * x_array * (1 + 0.0002 * x_array) ** 0.5
        elif pasquill_level == 'D':
            sigma_y_array = 0.08 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.06 * x_array * (1 + 0.0015 * x_array) ** 0.5
        elif pasquill_level == 'E':
            sigma_y_array = 0.06 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.03 * x_array * (1 + 0.0003 * x_array)
        elif pasquill_level == 'F':
            sigma_y_array = 0.04 * x_array * (1 + 0.0001 * x_array) ** 0.5
            sigma_z_array = 0.016 * x_array * (1 + 0.0003 * x_array)
        else:
            sigma_y_array = None
            sigma_z_array = None
    else:
        if pasquill_level == 'A' or pasquill_level == 'B':
            sigma_y_array = 0.32 * x_array * (1 + 0.0004 * x_array) ** -0.5
            sigma_z_array = 0.24 * x_array * (1 + 0.0001 * x_array) ** -0.5
        elif pasquill_level == 'C':
            sigma_y_array = 0.22 * x_array * (1 + 0.0004 * x_array) ** -0.5
            sigma_z_array = 0.20 * x_array
        elif pasquill_level == 'D':
            sigma_y_array = 0.16 * x_array * (1 + 0.0004 * x_array) ** -0.5
            sigma_z_array = 0.14 * x_array * (1 + 0.0003 * x_array) ** -0.5
        elif pasquill_level == 'E' or pasquill_level == 'F':
            sigma_y_array = 0.11 * x_array * (1 + 0.0004 * x_array) ** -0.5
            sigma_z_array = 0.08 * x_array * (1 + 0.0015 * x_array) ** -0.5
        else:
            sigma_y_array = None
            sigma_z_array = None
    
    return sigma_y_array, sigma_z_array
