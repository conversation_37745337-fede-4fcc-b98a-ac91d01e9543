<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b86a46d2-b3bf-4e6a-8d90-f60a13c7f128" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DatabaseView">
    <option name="SHOW_INTERMEDIATE" value="true" />
    <option name="GROUP_DATA_SOURCES" value="true" />
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <option name="AUTO_SCROLL_FROM_SOURCE" value="false" />
    <option name="HIDDEN_KINDS">
      <set />
    </option>
    <expand />
    <select />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="375">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/GaussianPlumeModel.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="484">
              <caret line="43" column="58" lean-forward="true" selection-start-line="43" selection-start-column="58" selection-end-line="43" selection-end-column="58" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/solve_model.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="437">
              <caret line="21" column="20" lean-forward="true" selection-start-line="21" selection-start-column="20" selection-end-line="21" selection-end-column="20" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/Briggs_diffusion_coefficient.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="330">
              <caret line="15" selection-start-line="15" selection-end-line="15" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/sun_inclination.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="792">
              <caret line="37" column="87" selection-start-line="37" selection-start-column="87" selection-end-line="37" selection-end-column="87" />
              <folding>
                <element signature="e#361#376#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/Pasquill_table.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="2068">
              <caret line="94" selection-start-line="94" selection-end-line="94" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/day_or_night.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="396">
              <caret line="18" column="4" selection-start-line="18" selection-start-column="4" selection-end-line="18" selection-end-column="4" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/sunlight_radiation_index.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="462">
              <caret line="21" column="43" selection-start-line="21" selection-start-column="43" selection-end-line="21" selection-end-column="43" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>sigma_z</find>
      <find>coll</find>
      <find>wind_speed</find>
    </findStrings>
    <replaceStrings>
      <replace>sigma_z_array</replace>
    </replaceStrings>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/../../../杂/四川大学/OCT/DataPocessing/Test/RawDataProcessing_0.py" />
        <option value="$PROJECT_DIR$/calculate_cloudage_index.py" />
        <option value="$PROJECT_DIR$/calculate_sunlight_radiation_index.py" />
        <option value="$PROJECT_DIR$/day_or_night.py" />
        <option value="$PROJECT_DIR$/sun_angle.py" />
        <option value="$PROJECT_DIR$/sun_inclination.py" />
        <option value="$PROJECT_DIR$/pasquill_table.py" />
        <option value="$PROJECT_DIR$/solve_model.py" />
        <option value="$PROJECT_DIR$/Pasquill_table.py" />
        <option value="$PROJECT_DIR$/Briggs_diffusion_coefficient.py" />
        <option value="$PROJECT_DIR$/GaussianPlumeModel.py" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="7">
    <option name="x" value="-8" />
    <option name="width" value="1295" />
    <option name="height" value="1088" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="Diffusion" type="b2602c69:ProjectViewProjectNode" />
              <item name="Diffusion" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/GaussianPlumeModel.py" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="reference.idesettings.debugger.python" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="GuassianPlumeModel" type="PythonConfigurationType" factoryName="Python">
      <module name="Test" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\ProgramData\Anaconda3\python.exe" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/GaussianPlumeModel.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b86a46d2-b3bf-4e6a-8d90-f60a13c7f128" name="Default Changelist" comment="" />
      <created>1652032344264</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1652032344264</updated>
      <workItem from="1652032347246" duration="4000" />
      <workItem from="1652276360147" duration="1957000" />
      <workItem from="1652326434658" duration="4000" />
      <workItem from="1652687222633" duration="42374000" />
      <workItem from="1652926225895" duration="17573000" />
      <workItem from="1652968353075" duration="7895000" />
      <workItem from="1653980951465" duration="8007000" />
      <workItem from="1654062210994" duration="4343000" />
      <workItem from="1654160392092" duration="1250000" />
      <workItem from="1654509490712" duration="11802000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="95209000" />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="2576" height="1048" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.061452515" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.17171717" />
      <window_info active="true" anchor="bottom" id="Debug" order="3" visible="true" weight="0.5634119" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" weight="0.32930756" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Database Changes" order="9" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Terminal" order="11" />
      <window_info anchor="bottom" id="Python Console" order="12" weight="0.32884398" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" auto_hide="true" x="300" y="193" width="1449" height="653" id="SciView" order="3" show_stripe_button="false" type="FLOATING" weight="0.5817308" />
      <window_info anchor="right" id="Database" order="4" weight="0.3299279" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/GaussianPlumeModel.py</url>
          <line>45</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Diffusion$GuassianPlumeModel.coverage" NAME="GuassianPlumeModel Coverage Results" MODIFIED="1654573821749" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/../../../杂/四川大学/OCT/DataPocessing/Test/RawDataProcessing.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="858">
          <caret line="45" column="69" selection-start-line="45" selection-start-column="69" selection-end-line="45" selection-end-column="69" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/../../../杂/四川大学/OCT/DataPocessing/Test/RawDataProcessing_0.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="726">
          <caret line="37" column="28" selection-start-line="37" selection-start-column="28" selection-end-line="37" selection-end-column="28" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/Lib/site-packages/matplotlib/cm.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1848" />
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/Lib/site-packages/mpl_toolkits/mplot3d/axes3d.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="295">
          <caret line="1555" selection-start-line="1555" selection-end-line="1555" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/Lib/codecs.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="187">
          <caret line="309" selection-start-line="309" selection-end-line="309" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/Briggs_diffusion_coefficient.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="330">
          <caret line="15" selection-start-line="15" selection-end-line="15" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/sun_inclination.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="792">
          <caret line="37" column="87" selection-start-line="37" selection-start-column="87" selection-end-line="37" selection-end-column="87" />
          <folding>
            <element signature="e#361#376#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/Pasquill_table.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2068">
          <caret line="94" selection-start-line="94" selection-end-line="94" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/day_or_night.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="396">
          <caret line="18" column="4" selection-start-line="18" selection-start-column="4" selection-end-line="18" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/sunlight_radiation_index.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="462">
          <caret line="21" column="43" selection-start-line="21" selection-start-column="43" selection-end-line="21" selection-end-column="43" />
        </state>
      </provider>
    </entry>
    <entry file="file://C:/ProgramData/Anaconda3/Lib/site-packages/matplotlib/pyplot.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="232">
          <caret line="2546" column="4" selection-start-line="2546" selection-start-column="4" selection-end-line="2546" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/solve_model.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="437">
          <caret line="21" column="20" lean-forward="true" selection-start-line="21" selection-start-column="20" selection-end-line="21" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/GaussianPlumeModel.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="484">
          <caret line="43" column="58" lean-forward="true" selection-start-line="43" selection-start-column="58" selection-end-line="43" selection-end-column="58" />
        </state>
      </provider>
    </entry>
  </component>
</project>