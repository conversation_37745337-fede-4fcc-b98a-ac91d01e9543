# -*- coding: utf-8 -*-

"""
@Project: Gaussian Plume Model
@Time: 2022/5/16 21:42
@Author: <PERSON>
@Contact: <EMAIL>
@Introduction: Please read documents below carefully before you dive into the code
1、物质的扩散-菲克定律 [<PERSON><PERSON>’s Law]
2、如何理解和区分旋度、散度和梯度？微分学中重要的概念
3、基于高斯烟羽模型的放射性气体的扩散
4、【国家标准】GBT 3840-1991 制定地方大气污染物排放标准的技术方法 标准
5、基于FICK定律和高斯烟羽模型的放射性气体扩散研究
"""


import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from day_or_night import determine_day_or_night
from sun_inclination import cal_sun_inclination
from sunlight_radiation_index import cal_sun_rad_index
from Pasquill_table import determine_pasquill_level
from Briggs_diffusion_coefficient import determine_briggs_diffusion_coefficient
from solve_model import model
plt.style.use("ggplot")


def output_contour_vertices(XX, YY, values_2D_array, levels=20, level_index=1):
    """
    :param XX: 2D meshgrid_x
    :param YY: 2D meshgrid_y
    :param values_2D_array: values in 2D array
    :param levels: the number of contour lines
    :param level_index: the contour line index you need to draw
    :return: coordinates of a specific contour line
    """
    contour = plt.contour(XX, YY, values_2D_array, levels=levels)
    clabel = plt.clabel(contour, levels=contour.levels, inline=True, fontsize=15)

    vertices = contour.collections[level_index].get_paths()[0].vertices
    vx = vertices[:, 0]     # contour line x array
    vy = vertices[:, 1]     # contour line y array
    cvalue = contour.cvalues[level_index]  # contour value

    plt.plot(vx, vy, '--', linewidth=3)

    return vx, vy

# ======= define parameters ======
# total cloudage level
tc = 5

# lower cloudage level
lc = 5

# wind speed [m/s]
wind_speed = 0
if wind_speed < 0.3:
    wind_speed = 0.3

# certain time lapse
t_certain = 600     # seconds

# latitude
lat = 25

# longitude
lon = 118

# Urban or rural
if_urban = False

# determine if_day
if_day = determine_day_or_night()

# determine solar altitude
h0 = cal_sun_inclination(latitude=lat, longitude=lon)

# determine sunlight_radiation_index
ci = cal_sun_rad_index(tc=tc, lc=lc, if_day=if_day, solar_altitude=h0)

# determine Pasquill level
pl = determine_pasquill_level(wind_speed=wind_speed, sun_rad_index=ci)


# ====== solve the Gaussian plume model ======
# define grid number along x direction (East)
# length_x = 30000    # meter
# grids_x = 1001
margin = 500
length_x = int(wind_speed * t_certain) + margin    # meter
grids_x = (int(wind_speed * t_certain) + margin) + 1

# define grid number along y direction (North)
length_y = 500    # meter
grids_y = 501

# define grid number along z direction (Elevation)
length_z = 20    # meter
grids_z = 11

# leaking point elevation
H = 10     # meter

# reflection coefficient
alpha = 1   # [0, 1]    0: no reflection; 1: total reflection

# define the time lapse
duration_time = 1000    # seconds
grids_time = 101

# leaking quantity [kg]
Q = 1

# leaking intensity [kg/s]
q = 0.1

# leaking location [m]
q_loc = (150, 200)

# diffusion speed
s = 0.0

# mesh
x = np.linspace(0, length_x, num=grids_x)[1:]
y = np.linspace(0, length_y, num=grids_y)[1:]
z = np.linspace(0, length_z, num=grids_z)[1:]
t = np.linspace(0, duration_time, num=grids_x)[1:]

X, Y, Z = np.meshgrid(x, y, z)

sigma_y, sigma_z = determine_briggs_diffusion_coefficient(x_array=X,
                                                          pasquill_level=pl,
                                                          if_urban=if_urban)
psi = model(X, Y, Z, sigma_y, sigma_z,
            leaking_loc=q_loc,
            t=t_certain,
            wind_speed=wind_speed,
            diffusion_speed=s,
            h=H,
            q=q)


for j in range(0, grids_z-1, 20):
    XX, YY = np.meshgrid(x + q_loc[0], y)
    values_2D_array = psi[:, :, j]
    values_2D_array[values_2D_array < 0] = 0

    fig1 = plt.figure(figsize=(10, 10))
    ax = Axes3D(fig1)
    ax.plot_surface(XX, YY, values_2D_array,
                    rstride=1,
                    cstride=1,
                    cmap=plt.cm.gist_stern)

    cs = ax.contourf(XX, YY, values_2D_array,
                     zdir='z',      # 使用数据方向
                     offset=-0.02,  # 填充投影轮廓位置
                     cmap=plt.cm.gist_ncar,
                     levels=100,
                     # vmin=0,
                     # vmax=0.4
                     )
    ax.scatter(q_loc[0], q_loc[1], marker='*', c='red', s=100, edgecolors='r')
    ax.set_xlabel('X [m]')
    ax.set_ylabel('Y [m]')
    ax.set_zlabel('Concentration [kg/m3]')
    ax.set_xlim(0, length_x + q_loc[0])
    ax.set_zlim(0, 0.2)
    ax.set_title(f"Elevation: {format(length_z/(grids_z-1) * (j+1), '.1f')} m, Time: {t_certain} sec")
    fig1.colorbar(cs, shrink=0.3, aspect=10)

    # out vertices from contour lines
    fig2 = plt.figure(figsize=(10, 10))
    vx, vy = output_contour_vertices(XX, YY, values_2D_array, levels=10, level_index=1)


plt.show()
